export type UnitType = "g" | "ml";

export interface Measurement {
  measurement_id: string;
  product_id: string;
  name: string;
  quantity: number;
  unit: UnitType;
  kcal: number;
  protein: number;
  carbs: number;
  fat: number;
  is_default: boolean;
}

export interface ProductFormData {
  name: string;
  mainUnit: UnitType;
  isShared: boolean;
  // Nutritional information per 100g (default)
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  // Additional measurements
  measurements: Omit<Measurement, "measurement_id" | "product_id">[];
}

export interface ProductWithMeasurements {
  id: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  isShared: boolean;
  mainUnit: UnitType;
  measurements: Measurement[];
}
