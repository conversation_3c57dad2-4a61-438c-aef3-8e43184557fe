"use client";

import * as React from "react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Measurement } from "@/types/Measurement";

interface ProductDetailsMeasurementMobileListItemProps {
  measurment: Measurement;
  className?: string;
}

export function ProductDetailsMeasurementMobileListItem({
  measurment: { name, is_default, kcal, protein, carbs, fat },
  className,
}: ProductDetailsMeasurementMobileListItemProps) {
  return (
    <div
      className={cn(
        "relative rounded-lg border bg-card p-4 shadow-sm",
        className
      )}
    >
      {/* Header with measurement name and badge */}
      <div className="flex items-center gap-3 mb-4">
        <h3 className="text-lg font-semibold text-foreground">{name}</h3>
        {is_default && (
          <Badge variant="secondary" className="text-xs">
            Podstawowa miara
          </Badge>
        )}
      </div>

      {/* Nutritional information grid */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-muted-foreground">Kalorie</p>
          <p className="text-base font-medium text-foreground">{kcal} kcal</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Białko</p>
          <p className="text-base font-medium text-foreground">{protein}g</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Węglowodany</p>
          <p className="text-base font-medium text-foreground">{carbs}g</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Tłuszcze</p>
          <p className="text-base font-medium text-foreground">{fat}g</p>
        </div>
      </div>
    </div>
  );
}
