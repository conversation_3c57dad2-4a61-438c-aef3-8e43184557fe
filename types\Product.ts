export interface Product {
  product_id: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  is_shared: boolean;
}

// Frontend type for product details
export interface ProductDetails {
  product_id: string;
  name: string;
  is_shared: boolean;
  default_measurement: {
    measurement_id: string;
    name: string;
    unit: "g" | "ml";
    kcal: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}
