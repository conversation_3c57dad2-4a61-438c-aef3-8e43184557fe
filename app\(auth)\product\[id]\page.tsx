"use client";
import PageHeader from "@/components/general/PageHeader";
import { PaginationComponent } from "@/components/general/PaginationComponent";
import { ProductDetailDefaultMeasurementCard } from "@/components/product/productDetail/ProductDetailDefaultMeasurmentCard";
import { ProductDetailsDefaultMeasurementChart } from "@/components/product/productDetail/ProductDetailsDefauldMeasurmentChart";
import { ProductDetailsMeasurmentMobileList } from "@/components/product/productDetail/ProductDetailsMeasurmentMobileList";
import { ProductDetailsMeasurementTable } from "@/components/product/productDetail/ProductDetailsMeasurmentTable";
import { Button } from "@/components/ui/button";
import PageWrapper from "@/components/wrappers/authPageWrapper";
import { SAMPLE_MEASUREMENTS } from "@/data/product/product";
import { useIsMobile } from "@/hooks/use-mobile";
import { getProductDetails } from "@/services/getProductsDetails";
import { Edit, TrashIcon } from "lucide-react";
import React, { use, useEffect } from "react";

interface ProductDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function ProductDetailPage({ params }: ProductDetailPageProps) {
  const { id } = use(params);
  const { product, isLoading, isError, refetch } = getProductDetails(id);
  const isMobile = useIsMobile();

  return (
    <PageWrapper>
      <PageHeader
        title={`Szczegóły produktu - ${product?.name}`}
        actions={
          <>
            <Button
              size={"icon"}
              variant={"outline"}
              disabled={product?.is_shared}
            >
              <Edit className="w-4 h-4" />
            </Button>
            <Button
              size={"icon"}
              variant={"outline"}
              className="text-destructive hover:text-destructive"
              disabled={product?.is_shared}
            >
              <TrashIcon className="w-4 h-4" />
            </Button>
          </>
        }
      />
      <div className="overflow-auto flex flex-col gap-4 p-4 flex-1">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:items-start">
          <ProductDetailDefaultMeasurementCard
            calories={product?.default_measurement.kcal || 0}
            protein={product?.default_measurement.protein || 0}
            carbs={product?.default_measurement.carbs || 0}
            fat={product?.default_measurement.fat || 0}
            unit={product?.default_measurement.unit || "g"}
          />
          <ProductDetailsDefaultMeasurementChart
            calories={product?.default_measurement.kcal || 0}
            protein={product?.default_measurement.protein || 0}
            carbs={product?.default_measurement.carbs || 0}
            fat={product?.default_measurement.fat || 0}
          />
        </div>
        {isMobile ? (
          <ProductDetailsMeasurmentMobileList
            measurements={SAMPLE_MEASUREMENTS}
          />
        ) : (
          <ProductDetailsMeasurementTable measurements={SAMPLE_MEASUREMENTS} />
        )}
        <PaginationComponent
          pageSize={10}
          totalItems={100}
          onPageChange={() => console.log("Page changed")}
        />
      </div>
    </PageWrapper>
  );
}
