"use client";

import * as React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Measurement } from "@/types/Measurement";

interface ProductDetailsMeasurementTableProps {
  measurements: Measurement[];
  className?: string;
}

export function ProductDetailsMeasurementTable({
  measurements,
  className,
}: ProductDetailsMeasurementTableProps) {
  return (
    <div className="rounded-lg border w-full ">
      <Table>
        <TableHeader className="bg-muted">
          <TableRow>
            <TableHead>Miara</TableHead>
            <TableHead>Ilość</TableHead>
            <TableHead>Kalorie</TableHead>
            <TableHead>Białko</TableHead>
            <TableHead>Węglowodany</TableHead>
            <TableHead>Tłuszcze</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {measurements.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={6}
                className="text-center py-8 text-muted-foreground"
              >
                Brak dodatkowych miar
              </TableCell>
            </TableRow>
          ) : (
            measurements.map((measurement) => (
              <TableRow key={measurement.measurement_id}>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <span>{measurement.name}</span>
                    {measurement.is_default && (
                      <Badge variant="secondary" className="text-xs">
                        Podstawowa miara
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {measurement.quantity}
                  {measurement.unit}
                </TableCell>
                <TableCell>{measurement.kcal} kcal</TableCell>
                <TableCell>{measurement.protein}g</TableCell>
                <TableCell>{measurement.carbs}g</TableCell>
                <TableCell>{measurement.fat}g</TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
