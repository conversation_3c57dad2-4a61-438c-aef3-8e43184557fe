"use client";

import * as React from "react";
import { <PERSON><PERSON><PERSON>, Pie, Cell } from "recharts";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig,
} from "@/components/ui/chart";
import { cn } from "@/lib/utils";

interface ProductDetailsDefaultMeasurementChartProps {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  className?: string;
}

export function ProductDetailsDefaultMeasurementChart({
  calories,
  protein,
  carbs,
  fat,
  className,
}: ProductDetailsDefaultMeasurementChartProps) {
  // Calculate total macronutrients for percentage calculation
  const totalMacros = protein + carbs + fat;

  // Calculate percentages
  const proteinPercentage = Math.round((protein / totalMacros) * 100);
  const carbsPercentage = Math.round((carbs / totalMacros) * 100);
  const fatPercentage = Math.round((fat / totalMacros) * 100);

  // Chart data
  const chartData = [
    {
      name: "protein",
      value: protein,
      percentage: proteinPercentage,
      fill: "var(--color-chart-1)",
    },
    {
      name: "carbs",
      value: carbs,
      percentage: carbsPercentage,
      fill: "var(--color-chart-2)",
    },
    {
      name: "fat",
      value: fat,
      percentage: fatPercentage,
      fill: "var(--color-chart-3)",
    },
  ];

  const chartConfig = {
    protein: {
      label: "Białko",
      color: "hsl(var(--color-chart-1))",
    },
    carbs: {
      label: "Węglowodany",
      color: "hsl(var(--color-chart-2))",
    },
    fat: {
      label: "Tłuszcze",
      color: "hsl(var(--color-chart-3))",
    },
  } satisfies ChartConfig;

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle>Podział makroskładników</CardTitle>
      </CardHeader>
      <CardContent className="flex">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[300px]"
        >
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={100}
              paddingAngle={2}
              dataKey="value"
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Pie>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            {/* Center text showing total calories */}
            <text
              x="50%"
              y="45%"
              textAnchor="middle"
              dominantBaseline="middle"
              className="fill-foreground text-3xl font-bold"
            >
              {calories}
            </text>
            <text
              x="50%"
              y="55%"
              textAnchor="middle"
              dominantBaseline="middle"
              className="fill-muted-foreground text-sm"
            >
              kcal
            </text>
          </PieChart>
        </ChartContainer>

        {/* Custom Legend */}
        <div className="flex flex-col justify-center gap-6 mt-4">
          <div className="flex flex-col items-center">
            <div className="w-4 h-4 rounded-sm bg-chart-1 mb-1"></div>
            <span className="text-sm font-medium">Białko</span>
            <span className="text-xs text-muted-foreground">
              {protein}g ({proteinPercentage}%)
            </span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-4 h-4 rounded-sm bg-chart-2 mb-1"></div>
            <span className="text-sm font-medium">Węglowodany</span>
            <span className="text-xs text-muted-foreground">
              {carbs}g ({carbsPercentage}%)
            </span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-4 h-4 rounded-sm bg-chart-3 mb-1"></div>
            <span className="text-sm font-medium">Tłuszcze</span>
            <span className="text-xs text-muted-foreground">
              {fat}g ({fatPercentage}%)
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export type { ProductDetailsDefaultMeasurementChartProps };
