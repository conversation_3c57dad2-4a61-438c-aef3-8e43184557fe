"use client";

import * as React from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { X, Plus, Trash2 } from "lucide-react";

import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { ProductFormData, UnitType } from "@/types/Measurement";

// Validation schema
const productSchema = yup.object({
  name: yup
    .string()
    .required("Nazwa produktu jest wymagana")
    .min(2, "Nazwa produktu musi mieć co najmniej 2 znaki"),
  mainUnit: yup
    .string()
    .oneOf(["g", "ml"], "Glówna jednostka musi być albo g albo ml")
    .required("Podstawowa jednostka jest wymagana"),
  isShared: yup.boolean().default(false),
  calories: yup
    .number()
    .required("Kalorie są wymagane")
    .min(0, "Kalorie muszą być dodatnie")
    .typeError("Kalorie muszą być liczbą"),
  protein: yup
    .number()
    .required("Białko jest wymagane")
    .min(0, "Białko musi być dodatnie")
    .typeError("Białko musi być liczbą"),
  carbs: yup
    .number()
    .required("Węglowodany są wymagane")
    .min(0, "Węglowodany muszą być dodatnie")
    .typeError("Węglowodany muszą być liczbą"),
  fat: yup
    .number()
    .required("Tłuszcze są wymagane")
    .min(0, "Tłuszcze muszą być dodatnie")
    .typeError("Tłuszcze muszą być liczbą"),
  measurements: yup
    .array()
    .of(
      yup.object({
        name: yup.string().required("Nazwa miary jest wymagana"),
        quantity: yup
          .number()
          .integer("Ilość musi być liczbą całkowitą")
          .required("Ilość jest wymagana")
          .positive("Ilość musi być dodatnia")
          .min(1, "Ilość musi być co najmniej 1")
          .typeError("Ilość musi być liczbą"),
        unit: yup
          .string()
          .oneOf(["g", "ml"], "Jednostka musi być albo g albo ml")
          .required("Jednostka jest wymagana"),
        kcal: yup
          .number()
          .required("Kalorie są wymagane")
          .min(0, "Kalorie muszą być dodatnie"),
        protein: yup
          .number()
          .required("Białko jest wymagane")
          .min(0, "Białko musi być dodatnie"),
        carbs: yup
          .number()
          .required("Węglowodany są wymagane")
          .min(0, "Węglowodany muszą być dodatnie"),
        fat: yup
          .number()
          .required("Tłuszcze są wymagane")
          .min(0, "Tłuszcze muszą być dodatnie"),
        is_default: yup.boolean().default(false),
      })
    )
    .default([]),
});

interface ProductAddEditPopupProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  product?: ProductFormData;
  onSubmit: (data: ProductFormData) => void;
  mode?: "add" | "edit";
}

export function ProductAddEditPopup({
  open,
  onOpenChange,
  product,
  onSubmit,
  mode = "add",
}: ProductAddEditPopupProps) {
  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<ProductFormData>({
    resolver: yupResolver(productSchema),
    defaultValues: {
      name: "",
      mainUnit: "g",
      isShared: false,
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0,
      measurements: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "measurements",
  });

  const watchedMainUnit = watch("mainUnit");
  const watchedCalories = watch("calories");
  const watchedProtein = watch("protein");
  const watchedCarbs = watch("carbs");
  const watchedFat = watch("fat");

  // Reset form when product changes or dialog opens
  React.useEffect(() => {
    if (product && mode === "edit") {
      reset(product);
    } else if (!product && mode === "add") {
      reset({
        name: "",
        mainUnit: "g",
        isShared: false,
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        measurements: [],
      });
    }
  }, [product, mode, reset]);

  const calculateMeasurementNutrition = (quantity: number, unit: UnitType) => {
    // Calculate nutrition based on the base 100g values
    const ratio = unit === watchedMainUnit ? quantity / 100 : quantity / 100;
    return {
      kcal: Math.round(watchedCalories * ratio * 100) / 100,
      protein: Math.round(watchedProtein * ratio * 100) / 100,
      carbs: Math.round(watchedCarbs * ratio * 100) / 100,
      fat: Math.round(watchedFat * ratio * 100) / 100,
    };
  };

  const addMeasurement = () => {
    const nutrition = calculateMeasurementNutrition(1, watchedMainUnit);
    append({
      name: "",
      quantity: 1,
      unit: watchedMainUnit,
      ...nutrition,
      is_default: false,
    });
  };

  const updateMeasurementNutrition = (
    index: number,
    quantity: number,
    unit: UnitType
  ) => {
    const nutrition = calculateMeasurementNutrition(quantity, unit);
    setValue(`measurements.${index}.kcal`, nutrition.kcal);
    setValue(`measurements.${index}.protein`, nutrition.protein);
    setValue(`measurements.${index}.carbs`, nutrition.carbs);
    setValue(`measurements.${index}.fat`, nutrition.fat);
  };

  const onFormSubmit = (data: ProductFormData) => {
    onSubmit(data);
    onOpenChange(false);
    reset();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        onInteractOutside={() => {
          onOpenChange(false);
          reset();
        }}
        className="max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        <DialogHeader>
          <DialogTitle>
            {mode === "add" ? "Dodaj nowy produkt" : "Edytuj produkt"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
          {/* Product Name */}
          <div className="space-y-2">
            <Label htmlFor="name">
              Nazwa produktu <span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              placeholder="Wprowadź nazwę produktu"
              {...register("name")}
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>

          {/* Main Unit */}
          <div className="space-y-3">
            <Label>
              Podstawowa jednostka <span className="text-red-500">*</span>
            </Label>
            <RadioGroup
              value={watchedMainUnit}
              onValueChange={(value: UnitType) => setValue("mainUnit", value)}
              className="flex gap-6"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="g" id="gram" />
                <Label htmlFor="gram">Gram (g)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="ml" id="milliliter" />
                <Label htmlFor="milliliter">Milliliter (ml)</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Share Product Checkbox */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="isShared"
              checked={watch("isShared")}
              onCheckedChange={(checked) => setValue("isShared", !!checked)}
            />
            <Label htmlFor="isShared" className="text-sm">
              Udostępnij produkt
            </Label>
          </div>
          <p className="text-xs text-muted-foreground">
            Udostępnione produkty nie mogą być edytowane lub usuwane później.
          </p>

          {/* Nutritional Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Wartości odżywcze</h3>

            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium">
                  Na 100{watchedMainUnit} (Podstawowa miara)
                </h4>
                <span className="text-xs text-muted-foreground">
                  Nie można usunąć
                </span>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="calories">
                    Calories (kcal) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="calories"
                    type="number"
                    step="0.1"
                    placeholder="0"
                    {...register("calories", {
                      valueAsNumber: true,
                    })}
                    className={errors.calories ? "border-red-500" : ""}
                  />
                  {errors.calories && (
                    <p className="text-sm text-red-500">
                      {errors.calories.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="protein">
                    Protein (g) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="protein"
                    type="number"
                    step="0.1"
                    placeholder="0"
                    {...register("protein", { valueAsNumber: true })}
                    className={errors.protein ? "border-red-500" : ""}
                  />
                  {errors.protein && (
                    <p className="text-sm text-red-500">
                      {errors.protein.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="carbs">
                    Carbohydrates (g) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="carbs"
                    type="number"
                    step="0.1"
                    placeholder="0"
                    {...register("carbs", { valueAsNumber: true })}
                    className={errors.carbs ? "border-red-500" : ""}
                  />
                  {errors.carbs && (
                    <p className="text-sm text-red-500">
                      {errors.carbs.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fat">
                    Fat (g) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="fat"
                    type="number"
                    step="0.1"
                    placeholder="0"
                    {...register("fat", { valueAsNumber: true })}
                    className={errors.fat ? "border-red-500" : ""}
                  />
                  {errors.fat && (
                    <p className="text-sm text-red-500">{errors.fat.message}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Additional Measurements */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Dodatkowe miary</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addMeasurement}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Dodaj miarę
              </Button>
            </div>

            {fields.length === 0 && (
              <p className="text-sm text-muted-foreground">
                Brak dodatkowych miar.
              </p>
            )}

            {fields.map((field, index) => (
              <div key={field.id} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Miara {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => remove(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`measurements.${index}.name`}>
                      Nazwa miary
                    </Label>
                    <Input
                      placeholder="e.g., tablespoon, cup, piece"
                      {...register(`measurements.${index}.name`)}
                      className={
                        errors.measurements?.[index]?.name
                          ? "border-red-500"
                          : ""
                      }
                    />
                    {errors.measurements?.[index]?.name && (
                      <p className="text-sm text-red-500">
                        {errors.measurements[index]?.name?.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`measurements.${index}.quantity`}>
                      Ilość ({watchedMainUnit})
                    </Label>
                    <Input
                      type="number"
                      step="0.1"
                      placeholder="10"
                      {...register(`measurements.${index}.quantity`, {
                        valueAsNumber: true,
                        onChange: (e) => {
                          const quantity = parseFloat(e.target.value) || 0;
                          const unit = watch(
                            `measurements.${index}.unit`
                          ) as UnitType;
                          updateMeasurementNutrition(index, quantity, unit);
                        },
                      })}
                      className={
                        errors.measurements?.[index]?.quantity
                          ? "border-red-500"
                          : ""
                      }
                    />
                    {errors.measurements?.[index]?.quantity && (
                      <p className="text-sm text-red-500">
                        {errors.measurements[index]?.quantity?.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Calculated nutrition values (read-only) */}
                <div className="bg-muted/30 p-3 rounded">
                  <p className="text-xs text-muted-foreground mb-2">
                    Obliczone wartości odżywcze:
                  </p>
                  <div className="grid grid-cols-4 gap-2 text-xs">
                    <div>
                      <span className="font-medium">Kalorie:</span>{" "}
                      {watch(`measurements.${index}.kcal`) || 0} kcal
                    </div>
                    <div>
                      <span className="font-medium">Białko:</span>{" "}
                      {watch(`measurements.${index}.protein`) || 0}g
                    </div>
                    <div>
                      <span className="font-medium">Węglowodany:</span>{" "}
                      {watch(`measurements.${index}.carbs`) || 0}g
                    </div>
                    <div>
                      <span className="font-medium">Tłuszcze:</span>{" "}
                      {watch(`measurements.${index}.fat`) || 0}g
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                onOpenChange(false);
                reset();
              }}
            >
              Anuluj
            </Button>
            <Button type="submit">
              {mode === "add" ? "Dodaj produkt" : "Aktualizuj produkt"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
