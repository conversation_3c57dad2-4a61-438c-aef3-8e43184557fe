import { createClient } from "@/utlis/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;

  // Convert string id to number for database query
  const productId = parseInt(id);
  if (isNaN(productId)) {
    return NextResponse.json({ error: "Invalid product ID" }, { status: 400 });
  }

  try {
    const supabase = await createClient();

    // Query product with its default measurement
    const { data: product, error } = await supabase
      .from("products")
      .select(
        `
        product_id,
        name,
        is_shared,
        measurements!inner(
          name,
          unit,
          kcal,
          protein,
          carbs,
          fat
        )
      `
      )
      .eq("product_id", productId)
      .eq("measurements.is_default", true)
      .single();

    if (error) {
      console.error("Database error:", error);
      return NextResponse.json(
        { error: "Failed to fetch product" },
        { status: 500 }
      );
    }

    if (!product) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 });
    }

    // Return the product with its default measurement
    return NextResponse.json({
      product_id: product.product_id,
      name: product.name,
      is_shared: product.is_shared,
      default_measurement: product.measurements[0],
    });
  } catch (err) {
    console.error("Unexpected error:", err);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
