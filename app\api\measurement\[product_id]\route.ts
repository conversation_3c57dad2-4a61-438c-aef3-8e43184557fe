import { createClient } from "@/utlis/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ product_id: string }> }
) {
  const searchParams = request.nextUrl.searchParams;
  const page = parseInt(searchParams.get("page") || "1");
  const pageSize = 10;
  const offset = (page - 1) * pageSize;

  const { product_id } = await params;
  // Convert string id to number for database query
  const productId = parseInt(product_id);
  if (isNaN(productId)) {
    return NextResponse.json({ error: "Invalid product ID" }, { status: 400 });
  }

  try {
    const supabase = await createClient();
    let query = supabase
      .from("measurements")
      .select("measurement_id, name, quantity, unit, kcal, protein, carbs, fat, is_default", { count: "exact" })
      .eq("product_id", productId);
    query = query.range(offset, offset + pageSize);
    const { data: measurements, error, count } = await query;
    if (error) {
      throw error;
    }
    return NextResponse.json({ measurements, count });
  } catch (err) {
    console.error("Unexpected error:", err);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
