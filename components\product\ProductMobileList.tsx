import React from 'react'
import { ProductMobileListItem } from './ProductMobileListItem';
import { Product } from '@/types/Product';

interface ProductMobileListProps {
  products: Product[];
  onEdit?: (product: Product) => void;
  onDelete?: (productId: string) => void;
  className?: string;
}

export const ProductMobileList = ({
  products,
  onEdit,
  onDelete,
  className,
}: ProductMobileListProps) => {
  return (
    <div className='flex flex-col gap-4 flex-1 overflow-auto p-4'>
        {products.map((product) => (
          <ProductMobileListItem
            key={product.product_id}
            product={product}
            onEdit={onEdit}
            onDelete={onDelete}
          />
        ))}
    </div>
  )
}
