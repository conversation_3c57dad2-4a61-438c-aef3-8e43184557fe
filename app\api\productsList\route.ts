import { createClient } from "@/utlis/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const searchQuery = searchParams.get("searchQuery");
  const myProducts = searchParams.get("myProducts") === "true";
  const sortBy = searchParams.get("sort") || "name-asc";

  const page = parseInt(searchParams.get("page") || "1");
  const pageSize = 10;
  const offset = (page - 1) * pageSize;
  try {
    const supabase = await createClient();
    const { data: user } = await supabase.auth.getUser();
    let query = supabase
      .from("products")
      .select(
        "product_id, name, is_shared , measurements(kcal, protein, carbs, fat)",
        { count: "exact" }
      )
      .eq("measurements.is_default", true);

    if (searchQuery) {
      query = query.ilike("name", `%${searchQuery}%`);
    }

    if (myProducts && user?.user) {
      query = query.eq("created_by", user?.user.id);
    }

    if (sortBy === "name-desc") {
      query = query.order("name", { ascending: false });
    } else {
      query = query.order("name", { ascending: true });
    }
    query = query.range(offset, offset + pageSize);
    let { data: products, error, count } = await query;
    if (error) {
      throw error;
    }
    return NextResponse.json({ products, count });
  } catch (err) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
