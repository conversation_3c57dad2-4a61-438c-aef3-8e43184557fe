import React from "react";
import { Measurement } from "@/types/Measurement";
import { ProductDetailsMeasurementMobileListItem } from "./ProductDetailsMeasurmentMobileListItem";

interface ProductDetailsMeasurmentMobileListProps {
  measurements: Measurement[];
  className?: string;
}

export const ProductDetailsMeasurmentMobileList = ({
  measurements,
  className,
}: ProductDetailsMeasurmentMobileListProps) => {
  return (
    <div className="flex flex-col gap-4 flex-1">
      {measurements.map((measurement) => (
        <ProductDetailsMeasurementMobileListItem
          key={measurement.measurement_id}
          measurment={measurement}
        />
      ))}
    </div>
  );
};
