"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { createClient } from "@/utlis/supabase/client";
import { useState } from "react";

export default function Home() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleLogin = async () => {
    console.log(email, password);
    const supabase = await createClient();
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email,
      password: password,
    });
    console.log(data);
    console.log(error);
  };
  return (
    <div className="w-screen h-full">
      <Input
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        type="text"
      />
      <Input
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        type="password"
      />
      <Button onClick={handleLogin}>Zaloguj</Button>
    </div>
  );
}
