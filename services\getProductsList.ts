import useSWR from 'swr'
import { useSearchParams } from 'next/navigation'
import { Product } from '@/types/Product';
import { useEffect } from 'react';

// const fetcher = (url: string) => fetch(url).then(res => res.json())

type ApiProduct = {
    product_id: number;
    name: string;
    is_shared: boolean;
    measurements: {
      kcal: number;
      protein: number;
      carbs: number;
      fat: number;
    }[];
  }[] & { count: number };

interface ApiProductResponse {
  products: ApiProduct;
  count: number;
}

interface ProductResponse {
  products: Product[];
  count: number;
}

const fetcher = async (url: string): Promise<ProductResponse> => {
  const res = await fetch(url);
  if (!res.ok) throw new Error('Failed to fetch');
  const apiProducts: ApiProductResponse = await res.json();
  // mapowanie na frontendowy typ Product
  return {
    products: apiProducts.products.map(apiProduct => {
      const measurement = apiProduct.measurements[0];
      return {
        product_id: apiProduct.product_id.toString(),
        name: apiProduct.name,
        calories: measurement.kcal,
        protein: measurement.protein,
        carbs: measurement.carbs,
        fat: measurement.fat,
        is_shared: apiProduct.is_shared,
      };
    }),
    count: apiProducts.count,
  };
};

export function getProductsList() {
  const searchParams = useSearchParams()

  
  
  // Build query string from search params
  const queryString = searchParams.toString()
  const url = `/api/productsList${queryString ? `?${queryString}` : ''}`
  
  const { data, error, isLoading, mutate } = useSWR<ProductResponse>(url, fetcher)
  
  return {
    products: data?.products || [],
    count: data?.count || 0,
    isLoading,
    isError: error,
    refetch: mutate
  }
}