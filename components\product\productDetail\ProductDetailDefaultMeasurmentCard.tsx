"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ProductDetailDefaultMeasurementCardProps {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  unit: string;
  className?: string;
}

export function ProductDetailDefaultMeasurementCard({
  calories,
  protein,
  carbs,
  fat,
  unit,
  className,
}: ProductDetailDefaultMeasurementCardProps) {
  return (
    <Card className={cn("w-full", className)}>
      {/* Header */}
      <CardHeader>
        <CardTitle>Wartości odżywcze (100{unit})</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Nutrition information */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-base text-foreground">Kalorie</span>
            <span className="text-base font-medium text-foreground">
              {calories} kcal
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-base text-foreground">Białko</span>
            <span className="text-base font-medium text-foreground">
              {protein}g
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-base text-foreground">Węglowodany</span>
            <span className="text-base font-medium text-foreground">
              {carbs}g
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-base text-foreground">Tłuszcze</span>
            <span className="text-base font-medium text-foreground">
              {fat}g
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export type { ProductDetailDefaultMeasurementCardProps };
